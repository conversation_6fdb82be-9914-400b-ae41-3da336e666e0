from fastapi import APIRouter, HTTPException, UploadFile, File
from fastapi.responses import StreamingResponse
from typing import Dict, List, Any, Optional
import json
import io
import logging
import traceback
from app.utils.frontend_log import post_log_to_frontend, post_generation_progress
from PIL import Image

from app.services.comfyui_service import ComfyUIService
from app.services.ollama_service import OllamaService
from app.services.system_monitor import SystemMonitor
from .prompt_enhancement_routes import router as prompt_enhancement_router
from .semantic_routes import router as semantic_router

# Configure detailed logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

router = APIRouter()

# Include prompt enhancement routes
router.include_router(prompt_enhancement_router)
router.include_router(semantic_router)

# Initialize services
comfyui_service = ComfyUIService()
ollama_service = OllamaService()
system_monitor = SystemMonitor()

@router.get("/health")
async def health_check():
    """Health check endpoint"""
    comfyui_status = await comfyui_service.check_connection()
    ollama_status = await ollama_service.check_connection()
    
    return {
        "status": "healthy",
        "services": {
            "comfyui": "connected" if comfyui_status else "disconnected",
            "ollama": "connected" if ollama_status else "disconnected"
        }
    }

@router.get("/")
async def api_info():
    """API information endpoint"""
    return {
        "name": "ComfyUI Custom Frontend API",
        "version": "1.0.0",
        "description": "Backend API for ComfyUI Custom Frontend with AI-powered features",
        "endpoints": {
            "health": "/api/v1/health",
            "system": "/api/v1/system/stats",
            "comfyui": "/api/v1/comfyui/status",
            "models": "/api/v1/models"
        }
    }

@router.get("/comfyui/status")
async def get_comfyui_status():
    """Get ComfyUI connection status"""
    is_connected = await comfyui_service.check_connection()
    return {
        "status": "connected" if is_connected else "disconnected",
        "service": "comfyui"
    }

@router.get("/models")
async def get_all_models():
    """Get all available models"""
    try:
        comfyui_models = await comfyui_service.scan_models()
        ollama_models = await ollama_service.list_models()
        
        return {
            "comfyui_models": comfyui_models,
            "ollama_models": ollama_models,
            "total_models": len(comfyui_models.get("models", [])) + len(ollama_models)
        }
    except Exception as e:
        logger.error(f"Error getting models: {e}")
        return {
            "comfyui_models": {"models": []},
            "ollama_models": [],
            "total_models": 0,
            "error": str(e)
        }

@router.get("/system/stats")
async def get_system_stats():
    """Get current system statistics"""
    return await system_monitor.get_current_stats()

@router.get("/system/health")
async def get_system_health():
    """Get system health status"""
    return system_monitor.check_system_health()

# ComfyUI Routes
@router.get("/comfyui/queue")
async def get_comfyui_queue():
    """Get ComfyUI queue status"""
    return await comfyui_service.get_queue_status()

@router.get("/comfyui/models")
async def get_available_models():
    """Get available models"""
    return await comfyui_service.scan_models()

@router.post("/comfyui/generate/text-to-image")
async def generate_text_to_image(request: Dict[str, Any]):
    """Generate image from text prompt with enhanced progress tracking"""
    try:
        # Initial setup and validation
        post_generation_progress(0, "initializing", "Received generation request")
        logger.info(f"Received text-to-image request")

        # Extract workflow from request (sent by frontend)
        workflow = request.get("workflow")
        if not workflow:
            post_log_to_frontend("❌ No workflow provided in request", "error")
            raise HTTPException(status_code=400, detail="Workflow is required")

        post_generation_progress(10, "processing", "Validating workflow parameters")
        post_log_to_frontend("🔍 Validating workflow parameters...")

        # Extract basic info for logging
        prompt_node = next((node for node in workflow.get("nodes", []) if node.get("type") == "Prompt"), None)
        prompt_text = prompt_node.get("value", "Unknown") if prompt_node else "Unknown"

        post_generation_progress(20, "building", "Building ComfyUI workflow")
        post_log_to_frontend(f"🎨 Building workflow for prompt: {prompt_text[:50]}...")

        # For now, use the existing workflow building logic
        # TODO: Implement proper frontend workflow conversion
        prompt = prompt_text if prompt_text != "Unknown" else ""
        if not prompt:
            post_log_to_frontend("❌ No prompt found in workflow", "error")
            raise HTTPException(status_code=400, detail="Prompt is required")

        # Extract other parameters from workflow nodes
        model_node = next((node for node in workflow.get("nodes", []) if node.get("type") == "ModelLoader"), None)
        sampler_node = next((node for node in workflow.get("nodes", []) if node.get("type") == "Sampler"), None)

        model = model_node.get("value", "flux1-kontext-dev.safetensors") if model_node else "flux1-kontext-dev.safetensors"
        sampler_config = sampler_node.get("value", {}) if sampler_node else {}

        width = sampler_config.get("width", 1024)
        height = sampler_config.get("height", 1024)
        steps = sampler_config.get("steps", 20)
        cfg = sampler_config.get("cfg", 7.0)
        seed = sampler_config.get("seed", -1)

        post_generation_progress(30, "building", "Converting to ComfyUI format")

        # Build workflow using existing service
        comfyui_workflow = comfyui_service.build_text_to_image_workflow(
            prompt=prompt,
            negative_prompt="",  # Extract from workflow if needed
            model=model,
            width=width,
            height=height,
            steps=steps,
            cfg=cfg,
            seed=seed
        )

        post_generation_progress(40, "submitting", "Submitting to ComfyUI")
        post_log_to_frontend("📤 Submitting workflow to ComfyUI...")

        # Submit to ComfyUI
        prompt_id = await comfyui_service.submit_workflow(comfyui_workflow)

        post_generation_progress(50, "generating", "Generation in progress")
        post_log_to_frontend(f"✅ Workflow submitted with ID: {prompt_id}")
        post_log_to_frontend("⏳ Generation in progress...")

        # For now, return the prompt_id and let frontend handle polling
        # TODO: Implement proper completion waiting with progress updates
        return {
            "success": True,
            "prompt_id": prompt_id,
            "status": "submitted",
            "message": "Generation started successfully"
        }

    except Exception as e:
        post_generation_progress(0, "failed", f"Error: {str(e)}")
        logger.error(f"Text-to-image generation failed: {str(e)}")
        post_log_to_frontend(f"❌ Generation error: {str(e)}", "error")
        logger.error(f"Full traceback: {traceback.format_exc()}")
        raise HTTPException(
            status_code=500,
            detail={
                "error": str(e),
                "type": type(e).__name__,
                "success": False
            }
        )

@router.get("/comfyui/status/{prompt_id}")
async def get_generation_status(prompt_id: str):
    """Get generation status"""
    try:
        status = await comfyui_service.get_workflow_status(prompt_id)
        images = await comfyui_service.get_generated_images(prompt_id)
        
        return {
            "prompt_id": prompt_id,
            "status": status,
            "images": images
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/comfyui/image/{filename}")
async def get_generated_image(filename: str, subfolder: str = "", type: str = "output"):
    """Download generated image"""
    try:
        image_data = await comfyui_service.download_image(filename, subfolder, type)
        
        return StreamingResponse(
            io.BytesIO(image_data),
            media_type="image/png",
            headers={"Content-Disposition": f"attachment; filename={filename}"}
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# Ollama Routes
@router.get("/ollama/models")
async def get_ollama_models():
    """Get available Ollama models"""
    try:
        models = await ollama_service.list_models()
        return {"models": models}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/ollama/generate")
async def generate_ollama_response(request: Dict[str, Any]):
    """Generate response using Ollama"""
    try:
        prompt = request.get("prompt", "")
        model = request.get("model")
        system_prompt = request.get("system_prompt", "")
        temperature = request.get("temperature", 0.7)
        max_tokens = request.get("max_tokens", 2000)
        
        if not prompt:
            raise HTTPException(status_code=400, detail="Prompt is required")
        
        response = await ollama_service.generate_response(
            prompt=prompt,
            model=model,
            system_prompt=system_prompt,
            temperature=temperature,
            max_tokens=max_tokens
        )
        
        return {"response": response}
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/ollama/creative-prompt")
async def generate_creative_prompt(request: Dict[str, Any]):
    """Generate creative prompt using AI"""
    try:
        base_theme = request.get("base_theme", "")
        style = request.get("style", "photorealistic")
        mood = request.get("mood", "dramatic")
        lighting = request.get("lighting", "cinematic")
        
        result = await ollama_service.generate_creative_prompt(
            base_theme=base_theme,
            style=style,
            mood=mood,
            lighting=lighting
        )
        
        return result
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/ollama/enhance-prompt")
async def enhance_prompt(request: Dict[str, Any]):
    """Enhance a basic prompt"""
    try:
        logger.info(f"Received prompt enhancement request: {request}")
        
        base_prompt = request.get("prompt", "")
        style_preferences = request.get("style_preferences", [])
        
        if not base_prompt:
            logger.error("No prompt provided for enhancement")
            raise HTTPException(status_code=400, detail="Prompt is required")
        
        logger.info(f"Enhancing prompt: {base_prompt[:100]}...")
        
        enhanced = await ollama_service.enhance_prompt(
            base_prompt=base_prompt,
            style_preferences=style_preferences
        )
        
        logger.info(f"Prompt enhanced successfully: {enhanced[:100]}...")
        
        return {"enhanced_prompt": enhanced}
        
    except Exception as e:
        logger.error(f"Prompt enhancement failed: {str(e)}")
        logger.error(f"Full traceback: {traceback.format_exc()}")
        raise HTTPException(
            status_code=500, 
            detail={
                "error": str(e),
                "type": type(e).__name__,
                "traceback": traceback.format_exc()
            }
        )

# Image Enhancement Routes
@router.post("/enhance/upscale")
async def upscale_image(
    file: UploadFile = File(...),
    model: str = "RealESRGAN_4x",
    scale: int = 4
):
    """Upscale an image using ComfyUI"""
    try:
        # Read uploaded image
        image_data = await file.read()
        
        # TODO: Implement image upscaling workflow
        # This would involve:
        # 1. Save uploaded image to temp location
        # 2. Build upscaling workflow
        # 3. Submit to ComfyUI
        # 4. Return prompt_id for status tracking
        
        return {
            "message": "Image upscaling not yet implemented",
            "filename": file.filename,
            "model": model,
            "scale": scale
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# Utility Routes
@router.post("/utils/analyze-image")
async def analyze_image(file: UploadFile = File(...)):
    """Analyze uploaded image for reference mode"""
    try:
        image_data = await file.read()
        
        # Open image with PIL
        image = Image.open(io.BytesIO(image_data))
        
        # Basic image analysis
        analysis = {
            "filename": file.filename,
            "format": image.format,
            "mode": image.mode,
            "size": image.size,
            "width": image.width,
            "height": image.height,
            "aspect_ratio": round(image.width / image.height, 2),
            "file_size_bytes": len(image_data),
            "estimated_colors": len(colors) if image.mode == "RGB" and (colors := image.getcolors(maxcolors=256*256*256)) else "N/A"
        }
        
        # TODO: Add more sophisticated analysis
        # - Color palette extraction
        # - Style detection
        # - Composition analysis
        
        return analysis
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# File Explorer Routes
@router.get("/explorer/folder")
async def get_folder_contents(path: str = "/"):
    """Get contents of a folder for the file explorer"""
    import os
    import pathlib
    from datetime import datetime
    
    try:
        # Define safe base directories
        base_dirs = {
            "/": "L:/ComfyUI/output",
            "output": "L:/ComfyUI/output", 
            "input": "L:/ComfyUI/input",
            "temp": "L:/ComfyUI/temp",
            "models": "L:/ComfyUI/models"
        }
        
        # Resolve the actual path
        if path in base_dirs:
            actual_path = base_dirs[path]
        elif path.startswith("output/"):
            actual_path = os.path.join("L:/ComfyUI/output", path[7:])
        elif path.startswith("input/"):
            actual_path = os.path.join("L:/ComfyUI/input", path[6:])
        elif path.startswith("temp/"):
            actual_path = os.path.join("L:/ComfyUI/temp", path[5:])
        elif path.startswith("models/"):
            actual_path = os.path.join("L:/ComfyUI/models", path[7:])
        else:
            actual_path = "L:/ComfyUI/output"  # Default fallback
        
        # Security check: ensure path is within allowed directories
        actual_path = os.path.abspath(actual_path)
        allowed_base = os.path.abspath("L:/ComfyUI")
        if not actual_path.startswith(allowed_base):
            raise HTTPException(status_code=403, detail="Access denied to path outside ComfyUI directory")
        
        # Check if directory exists
        if not os.path.exists(actual_path):
            raise HTTPException(status_code=404, detail=f"Directory not found: {path}")
        
        if not os.path.isdir(actual_path):
            raise HTTPException(status_code=400, detail=f"Path is not a directory: {path}")
        
        items = []
        
        try:
            for item_name in sorted(os.listdir(actual_path)):
                item_path = os.path.join(actual_path, item_name)
                
                # Skip hidden files and system files
                if item_name.startswith('.') or item_name.startswith('__'):
                    continue
                
                # Get file info
                stat_info = os.stat(item_path)
                modified = datetime.fromtimestamp(stat_info.st_mtime)
                
                is_dir = os.path.isdir(item_path)
                is_image = False
                thumbnail = None
                
                if not is_dir:
                    # Check if it's an image file
                    image_extensions = {'.png', '.jpg', '.jpeg', '.webp', '.gif', '.bmp', '.tiff'}
                    ext = pathlib.Path(item_name).suffix.lower()
                    is_image = ext in image_extensions
                    
                    # For images, we could generate thumbnails here
                    # For now, we'll use the full image path as thumbnail
                    if is_image:
                        # Convert absolute path to relative URL for frontend
                        thumbnail = f"/api/v1/comfyui/image/{item_name}?subfolder={path}&type=output"
                
                items.append({
                    "id": f"{path}/{item_name}",
                    "name": item_name,
                    "type": "folder" if is_dir else "image" if is_image else "file",
                    "path": os.path.join(path, item_name).replace("\\", "/"),
                    "size": stat_info.st_size if not is_dir else None,
                    "modified": modified.isoformat(),
                    "thumbnail": thumbnail
                })
                
        except PermissionError:
            raise HTTPException(status_code=403, detail=f"Permission denied accessing directory: {path}")
        
        return items
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error reading directory {path}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error reading directory: {str(e)}")

@router.post("/explorer/create-folder")
async def create_folder(request: Dict[str, Any]):
    """Create a new folder"""
    import os
    
    try:
        path = request.get("path", "")
        name = request.get("name", "")
        
        if not path or not name:
            raise HTTPException(status_code=400, detail="Path and name are required")
        
        # TODO: Implement folder creation with security checks
        return {"message": "Folder creation not yet implemented"}
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/explorer/delete")
async def delete_items(request: Dict[str, Any]):
    """Delete files or folders"""
    try:
        items = request.get("items", [])
        
        if not items:
            raise HTTPException(status_code=400, detail="No items specified for deletion")
        
        # TODO: Implement secure file deletion
        return {"message": "File deletion not yet implemented"}
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


