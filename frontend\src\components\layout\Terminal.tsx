'use client'

import React, { useState, useRef, useEffect } from 'react'
import { Terminal as TerminalIcon, X, Minimize2, Maximize2, <PERSON>ota<PERSON><PERSON>c<PERSON>, Copy } from 'lucide-react'
import { LayoutSettings } from './OptionsMenu'

interface TerminalProps {
  settings: LayoutSettings
  glassEffect: string
  shadowClass: string
  height?: number
  onResize?: (height: number) => void
  isMinimized?: boolean
  onMinimizeToggle?: (minimized: boolean) => void
}

const Terminal: React.FC<TerminalProps> = ({
  settings,
  glassEffect,
  shadowClass,
  height,
  onResize,
  isMinimized: externalIsMinimized,
  onMinimizeToggle
}) => {
  // Use external minimize state if provided, otherwise use internal state
  const [internalIsMinimized, setInternalIsMinimized] = useState(false)
  const isMinimized = externalIsMinimized !== undefined ? externalIsMinimized : internalIsMinimized
  const [history, setHistory] = useState<string[]>([
    '🚀 ComfyUI Custom Frontend Terminal',
    '📡 Backend: Connected (http://localhost:8000)',
    '🎨 ComfyUI: Ready (http://127.0.0.1:8188)',
    '✨ Frontend: Active (http://localhost:3000)',
    '📊 Models: 36 files discovered',
    '🤖 Ollama: Available for prompt enhancement',
    '',
    'Ready for image generation...'
  ])
  const [wsConnection, setWsConnection] = useState<WebSocket | null>(null)
  const [connectionStatus, setConnectionStatus] = useState<'connecting' | 'connected' | 'disconnected'>('disconnected')
  const [input, setInput] = useState('')
  const [isResizing, setIsResizing] = useState(false)
  const terminalRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLInputElement>(null)
  const resizeStartY = useRef<number>(0)
  const initialHeight = useRef<number>(0)


  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    if (terminalRef.current) {
      terminalRef.current.scrollTop = terminalRef.current.scrollHeight
    }
  }, [history])

  // WebSocket connection for real-time updates
  useEffect(() => {
    const connectWebSocket = () => {
      try {
        setConnectionStatus('connecting')
        const ws = new WebSocket('ws://localhost:8000/ws')

        ws.onopen = () => {
          setConnectionStatus('connected')
          setHistory(prev => [...prev, '🔗 WebSocket connected - Real-time updates enabled'])
        }

        ws.onmessage = (event) => {
          try {
            const data = JSON.parse(event.data)

            if (data.type === 'terminal_message') {
              const { message, log_type } = data.data
              const timestamp = new Date().toLocaleTimeString()
              const formattedMessage = `[${timestamp}] ${message}`
              setHistory(prev => [...prev, formattedMessage])
            } else if (data.type === 'generation_progress') {
              const { progress, status, message } = data.data
              const timestamp = new Date().toLocaleTimeString()
              const formattedMessage = `[${timestamp}] [${progress}%] ${status.toUpperCase()}: ${message}`
              setHistory(prev => [...prev, formattedMessage])
            }
          } catch (error) {
            console.error('Error parsing WebSocket message:', error)
          }
        }

        ws.onclose = () => {
          setConnectionStatus('disconnected')
          setHistory(prev => [...prev, '❌ WebSocket disconnected - Attempting to reconnect...'])
          // Attempt to reconnect after 3 seconds
          setTimeout(connectWebSocket, 3000)
        }

        ws.onerror = (error) => {
          console.error('WebSocket error:', error)
          setHistory(prev => [...prev, '⚠️ WebSocket error occurred'])
        }

        setWsConnection(ws)
      } catch (error) {
        console.error('Failed to create WebSocket connection:', error)
        setConnectionStatus('disconnected')
      }
    }

    connectWebSocket()

    return () => {
      if (wsConnection) {
        wsConnection.close()
      }
    }
  }, []) // Empty dependency array to run only once

  // Poll server logs every 2 seconds and append new lines (fallback)
  useEffect(() => {
    // Only use polling if WebSocket is not connected
    if (connectionStatus !== 'connected') {
      let lastLogCount = 0;
      const interval = setInterval(async () => {
        try {
          const res = await fetch('/api/logs');
          if (res.ok) {
            const data = await res.json();
            if (Array.isArray(data.logs) && data.logs.length > lastLogCount) {
              // Only append new logs
              setHistory(prev => [...prev, ...data.logs.slice(lastLogCount)]);
              lastLogCount = data.logs.length;
            }
          }
        } catch (e) {
          // Ignore fetch errors
        }
      }, 2000);
      return () => clearInterval(interval);
    }
  }, [connectionStatus]);

  const handleCommand = (command: string) => {
    const cmd = command.trim().toLowerCase()
    let response = ''

    switch (cmd) {
      case 'clear':
        setHistory([])
        return
      case 'help':
        response = `Available commands:
  • clear - Clear terminal
  • status - Show system status
  • models - List available models
  • test - Test API connections
  • generate - Start image generation
  • help - Show this help`
        break
      case 'status':
        response = `🔍 System Status:
  Backend: ✅ Running on :8000
  ComfyUI: ✅ Running on :8188
  Frontend: ✅ Running on :3000
  Models: ✅ 36 files loaded`
        break
      case 'models':
        response = `📦 Available Models:
  • flux1-kontext-dev.safetensors
  • Juggernaut-XL_v9_RunDiffusionPhoto_v2.safetensors
  • pixelwave_flux1_dev_fp8_03.safetensors
  • 14 LoRA files
  • 3 VAE models`
        break
      case 'test':
        response = `🧪 Testing connections...
  ✅ Backend health check: OK
  ✅ Model discovery: OK
  ✅ Ollama service: OK
  ✅ Frontend proxy: OK`
        break
      case 'generate':
        response = `🎨 Ready for generation! Use the UI panel to:
  1. Enter your prompt
  2. Select model and parameters
  3. Click "Generate Image"`
        break
      default:
        response = `Command not found: ${command}. Type 'help' for available commands.`
    }

    setHistory(prev => [...prev, `$ ${command}`, response, ''])
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      if (input.trim()) {
        handleCommand(input)
        setInput('')
      }
    }
  }

  const handleMouseDown = (e: React.MouseEvent) => {
    setIsResizing(true)
    resizeStartY.current = e.clientY
    initialHeight.current = height ?? 250
    document.addEventListener('mousemove', handleMouseMove)
    document.addEventListener('mouseup', handleMouseUp)
  }

  const handleMouseMove = (e: MouseEvent) => {
    if (!isResizing) return
    const deltaY = resizeStartY.current - e.clientY
    const newHeight = Math.max(200, Math.min(600, initialHeight.current + deltaY))
    onResize?.(newHeight)
  }

  const handleMouseUp = () => {
    setIsResizing(false)
    document.removeEventListener('mousemove', handleMouseMove)
    document.removeEventListener('mouseup', handleMouseUp)
  }

  const copyTerminalContent = () => {
    const content = history.join('\n')
    navigator.clipboard.writeText(content)
    setHistory(prev => [...prev, '📋 Terminal content copied to clipboard', ''])
  }

  return (
    <div
      className={`bg-gray-900/95 border-t border-gray-700/65 ${glassEffect} ${shadowClass} flex flex-col`}
      style={{ height: '100%', maxHeight: '100%', overflow: 'hidden' }}
    >
      {/* Terminal Header - Always visible */}
      <div className="flex-shrink-0 flex items-center justify-between px-4 py-2 border-b border-gray-700/50 bg-gray-800/50">
        <div className="flex items-center gap-2">
          <TerminalIcon className="w-4 h-4 text-green-400" />
          <span className="text-sm font-medium text-white">Terminal</span>
          <div className="flex gap-1 ml-2">
            <div className={`w-2 h-2 rounded-full ${
              connectionStatus === 'connected' ? 'bg-green-500 animate-pulse' :
              connectionStatus === 'connecting' ? 'bg-yellow-500 animate-pulse' :
              'bg-red-500'
            }`}></div>
            <span className={`text-xs ${
              connectionStatus === 'connected' ? 'text-green-400' :
              connectionStatus === 'connecting' ? 'text-yellow-400' :
              'text-red-400'
            }`}>
              {connectionStatus === 'connected' ? 'Live' :
               connectionStatus === 'connecting' ? 'Connecting' :
               'Offline'}
            </span>
          </div>
        </div>

        <div className="flex items-center gap-1">
          <button
            onClick={copyTerminalContent}
            className="p-1 hover:bg-gray-700/50 rounded text-gray-400 hover:text-white transition-colors"
            title="Copy terminal content"
          >
            <Copy className="w-3 h-3" />
          </button>
          <button
            onClick={() => setHistory([])}
            className="p-1 hover:bg-gray-700/50 rounded text-gray-400 hover:text-white transition-colors"
            title="Clear terminal"
          >
            <RotateCcw className="w-3 h-3" />
          </button>
          <button
            onClick={() => {
              if (onMinimizeToggle) {
                onMinimizeToggle(!isMinimized)
              } else {
                setInternalIsMinimized(!isMinimized)
              }
            }}
            className="p-1 hover:bg-gray-700/50 rounded text-gray-400 hover:text-white transition-colors"
          >
            {isMinimized ? <Maximize2 className="w-3 h-3" /> : <Minimize2 className="w-3 h-3" />}
          </button>
        </div>
      </div>

      {/* Resize Handle - Only show when not minimized */}
      {!isMinimized && (
        <div
          className="h-1 bg-gray-700/30 hover:bg-blue-500/50 cursor-ns-resize transition-colors"
          onMouseDown={handleMouseDown}
          title="Drag to resize terminal"
        />
      )}

      {/* Terminal Content - Only show when not minimized */}
      {!isMinimized && (
        <div className="flex-1 p-4 overflow-y-auto font-mono text-sm text-green-300 bg-black/20"
          ref={terminalRef}
          onClick={() => inputRef.current?.focus()}>
          {history.map((line, index) => (
            <div key={index} className={line.startsWith('$') ? 'text-yellow-300' : 'text-green-300'}>
              {line || '\u00A0'} {/* Non-breaking space for empty lines */}
            </div>
          ))}
        </div>
      )}

      {/* Terminal Input - Always visible at bottom */}
      <div className="flex-shrink-0 flex items-center gap-2 px-4 py-2 bg-black/30 border-t border-gray-700/30">
        <span className="text-yellow-300 font-mono">$</span>
        <input
          ref={inputRef}
          type="text"
          value={input}
          onChange={(e) => setInput(e.target.value)}
          onKeyPress={handleKeyPress}
          className="flex-1 bg-transparent text-green-300 font-mono outline-none placeholder-gray-500"
          placeholder="Type 'help' for available commands..."
          autoFocus
        />
      </div>
    </div>
  )
}

export default Terminal
