import requests
import os
import asyncio
from typing import Optional
import logging

logger = logging.getLogger(__name__)

FRONTEND_LOG_URL = os.environ.get('FRONTEND_LOG_URL', 'http://localhost:3000/api/logs')

# Global WebSocket manager reference (will be set by main.py)
websocket_manager: Optional['ConnectionManager'] = None

def set_websocket_manager(manager):
    """Set the WebSocket manager for real-time messaging"""
    global websocket_manager
    websocket_manager = manager

def post_log_to_frontend(message: str, log_type: str = "info"):
    """
    Post log message to frontend via both HTTP and WebSocket

    Args:
        message: The log message to send
        log_type: Type of log (info, warning, error, success, progress)
    """
    try:
        # HTTP fallback (existing method)
        requests.post(FRONTEND_LOG_URL, json={"message": message, "type": log_type}, timeout=2)
    except Exception as e:
        logger.debug(f"HTTP log post failed: {e}")

    # WebSocket real-time messaging
    if websocket_manager:
        try:
            import json
            ws_message = json.dumps({
                "type": "terminal_message",
                "data": {
                    "message": message,
                    "log_type": log_type,
                    "timestamp": __import__('time').time()
                }
            })
            # Use asyncio to broadcast if we're in an async context
            try:
                loop = asyncio.get_event_loop()
                if loop.is_running():
                    asyncio.create_task(websocket_manager.broadcast(ws_message))
                else:
                    asyncio.run(websocket_manager.broadcast(ws_message))
            except RuntimeError:
                # If no event loop, try to create one
                asyncio.run(websocket_manager.broadcast(ws_message))
        except Exception as e:
            logger.debug(f"WebSocket broadcast failed: {e}")

def post_generation_progress(progress: int, status: str, message: str = ""):
    """
    Post generation progress update to frontend

    Args:
        progress: Progress percentage (0-100)
        status: Current status (generating, completed, failed)
        message: Optional status message
    """
    if websocket_manager:
        try:
            import json
            ws_message = json.dumps({
                "type": "generation_progress",
                "data": {
                    "progress": progress,
                    "status": status,
                    "message": message,
                    "timestamp": __import__('time').time()
                }
            })
            # Use asyncio to broadcast
            try:
                loop = asyncio.get_event_loop()
                if loop.is_running():
                    asyncio.create_task(websocket_manager.broadcast(ws_message))
                else:
                    asyncio.run(websocket_manager.broadcast(ws_message))
            except RuntimeError:
                asyncio.run(websocket_manager.broadcast(ws_message))
        except Exception as e:
            logger.debug(f"Progress broadcast failed: {e}")

    # Also log to terminal
    post_log_to_frontend(f"[{progress}%] {status}: {message}", "progress")
