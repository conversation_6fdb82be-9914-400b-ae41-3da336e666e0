'use client'

import { useState, useEffect } from 'react'
import standardWorkflow from '@/workflows/text_to_image_standard.json'
import fluxWorkflow from '@/workflows/text_to_image_flux.json'
import { Button } from '@/components/ui/Button'
import { Textarea } from '@/components/ui/Textarea'
import { Select } from '@/components/ui/Select'
import { Slider } from '@/components/ui/Slider'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import { Wand2, Settings, Download, RefreshCw, Sparkles, Menu } from 'lucide-react'
import { useWorkflow } from '@/hooks/useWorkflow'
import GeneratedImageDisplay from '@/components/generation/GeneratedImageDisplay'
import PostGenerationMenu from '@/components/generation/PostGenerationMenu'
import toast from 'react-hot-toast'

interface GenerationParams {
  prompt: string
  negativePrompt: string
  model: string
  lora: string
  vae: string
  textEncoderT5: string
  textEncoderViTL: string
  width: number
  height: number
  steps: number
  cfg: number
  seed: number
  sampler: string
}

interface GenerationResult {
  id: string
  imageUrl: string
  prompt: string
  timestamp: Date
  settings: {
    model: string
    steps: number
    guidance: number
    dimensions: string
    seed?: number
  }
  status: 'generating' | 'completed' | 'failed'
}

export default function TextToImageWorkspace() {
  const { setGenerationStatus, setProgress, generationProgress } = useWorkflow()
  const [wsConnection, setWsConnection] = useState<WebSocket | null>(null)
  const [params, setParams] = useState<GenerationParams>({
    prompt: '',
    negativePrompt: '',
    model: 'flux1-kontext-dev.safetensors',
    lora: '',
    vae: '',
    textEncoderT5: 'L:\\ComfyUI\\models\\clip\\t5xxl_fp8_e4m3fn.safetensors',
    textEncoderViTL: 'L:\\ComfyUI\\models\\clip\\flux-ViT-L-14-TEXT-detail-improved-hiT-GmP-TE-only-HF.safetensors',
    width: 1024,
    height: 1024,
    steps: 32,
    cfg: 7.0,
    seed: -1,
    sampler: 'DPM++ 2M Karras'
  })
  const [isGenerating, setIsGenerating] = useState(false)
  const [currentGeneration, setCurrentGeneration] = useState<GenerationResult | null>(null)
  const [availableModels, setAvailableModels] = useState<string[]>([])
  const [availableLoras, setAvailableLoras] = useState<string[]>([])
  const [availableVAEs, setAvailableVAEs] = useState<string[]>([])
  const [modelsLoading, setModelsLoading] = useState(false)
  const [modelsError, setModelsError] = useState<string | null>(null)
  const [showPostMenu, setShowPostMenu] = useState(false)
  const [isEnhancing, setIsEnhancing] = useState(false)

  // WebSocket connection for real-time progress updates
  useEffect(() => {
    const connectWebSocket = () => {
      try {
        const ws = new WebSocket('ws://localhost:8000/ws')

        ws.onopen = () => {
          console.log('TextToImage WebSocket connected')
        }

        ws.onmessage = (event) => {
          try {
            const data = JSON.parse(event.data)

            if (data.type === 'generation_progress') {
              const { progress, status, message } = data.data
              setProgress(progress)

              // Update generation status based on backend status
              if (status === 'completed') {
                setGenerationStatus('completed')
                setIsGenerating(false)
                toast.success('🎉 Image generated successfully!')
              } else if (status === 'failed') {
                setGenerationStatus('idle')
                setIsGenerating(false)
                toast.error('❌ Generation failed')
              } else if (status === 'generating') {
                setGenerationStatus('generating')
              }
            }
          } catch (error) {
            console.error('Error parsing WebSocket message:', error)
          }
        }

        ws.onclose = () => {
          console.log('TextToImage WebSocket disconnected')
          // Attempt to reconnect after 3 seconds
          setTimeout(connectWebSocket, 3000)
        }

        ws.onerror = (error) => {
          console.error('TextToImage WebSocket error:', error)
        }

        setWsConnection(ws)
      } catch (error) {
        console.error('Failed to create WebSocket connection:', error)
      }
    }

    connectWebSocket()

    return () => {
      if (wsConnection) {
        wsConnection.close()
      }
    }
  }, []) // Empty dependency array to run only once

  // Load available models on component mount
  useEffect(() => {
    fetchAvailableModels()
  }, [])

  const fetchAvailableModels = async () => {
    setModelsLoading(true)
    setModelsError(null)
    try {
      const response = await fetch('/api/comfyui/models')
      if (response.ok) {
        const models = await response.json()
        setAvailableModels(models.checkpoints || [])
        setAvailableLoras(models.loras || [])
        setAvailableVAEs(models.vae || [])
        if ((models.checkpoints?.length ?? 0) === 0) setModelsError('No models found')
      } else {
        setModelsError(`Failed to load models: ${response.statusText}`)
      }
    } catch (error) {
      setModelsError(`Failed to load models: ${error}`)
    }
    setModelsLoading(false)
  }

  const generateRandomSeed = () => {
    const newSeed = Math.floor(Math.random() * 1000000)
    setParams(prev => ({ ...prev, seed: newSeed }))
  }

  const handleGenerate = async () => {
    if (!params.prompt.trim()) {
      toast.error('Please enter a prompt')
      return
    }

    setIsGenerating(true)
    setGenerationStatus('generating')
    setProgress(0)

    try {
      // Step 1: Enhance prompt with Ollama (model-specific)
      toast('🤖 Enhancing prompt with AI...')
      setProgress(10)
      let enhancedPrompt = params.prompt
      try {
        const modelType = getModelType(params.model)
        const enhanceResponse = await fetch('/api/ollama/enhance-prompt', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            prompt: params.prompt,
            model: params.model,
            style_preferences: [modelType]
          })
        })
        if (enhanceResponse.ok) {
          const enhanceResult = await enhanceResponse.json()
          enhancedPrompt = enhanceResult.enhanced_prompt || params.prompt
          toast.success(`✨ Prompt enhanced for ${modelType.toUpperCase()}!`)
          setProgress(25)
        } else {
          toast('⚠️ Using original prompt (enhancement unavailable)')
        }
      } catch {
        toast('⚠️ Using original prompt (enhancement unavailable)')
      }

      // Step 2: Build workflow JSON from template
      toast('🎨 Building workflow...')
      setProgress(30)
      // Determine if model is Flux by filename (robust match)
      const fluxModelNames = [
        'flux1-schnell.safetensors',
        'fluxArtFusionNF4Fp8Fp16_v10Fp16.safetensors',
        'fluxFillFP8_v10.safetensors',
        'fluxmania_III(fluxDfp8).safetensors',
        'flux1-dev.safetensors',
        'flux1-kontext-dev-Q4_K_M.gguf',
        'flux1-kontext-dev.safetensors',
        'pixelwave_flux1_dev_fp8_03.safetensors'
      ];
      const modelFile = params.model.split(/\\|\//).pop()?.toLowerCase() || '';
      const isFlux = fluxModelNames.map(n => n.toLowerCase()).includes(modelFile);

      // Select correct workflow template
      const workflowTemplate = isFlux ? fluxWorkflow : standardWorkflow;
      // Deep clone to avoid mutating import
      let workflowNodes = JSON.parse(JSON.stringify(workflowTemplate.nodes));

      // Dynamically build nodes for this run
      const newNodes = [];
      for (const node of workflowNodes) {
        switch (node.type) {
          case 'Prompt':
            newNodes.push({ ...node, value: enhancedPrompt });
            break;
          case 'NegativePrompt':
            if (!isFlux) newNodes.push({ ...node, value: params.negativePrompt });
            break;
          case 'FluxGuidance':
            if (isFlux) newNodes.push({ ...node, value: {} }); // Add guidance params if needed
            break;
          case 'ModelLoader':
            newNodes.push({ ...node, value: params.model });
            break;
          case 'TextEncoderT5':
            if (!isFlux) newNodes.push({ ...node, value: params.textEncoderT5 });
            break;
          case 'TextEncoderViT-L':
            if (!isFlux) newNodes.push({ ...node, value: params.textEncoderViTL });
            break;
          case 'LoRALoader':
            if (params.lora) newNodes.push({ ...node, value: params.lora });
            break;
          case 'VAE':
            // Auto-select VAE for Flux/SDXL
            let vaePath = params.vae;
            if (isFlux) {
              vaePath = 'L:\\ComfyUI\\models\\vae\\Flux_vae.safetensors';
            } else {
              vaePath = 'L:\\ComfyUI\\models\\vae\\sdxl_vae.safetensors';
            }
            newNodes.push({ ...node, value: vaePath });
            break;
          case 'Sampler':
            newNodes.push({
              ...node,
              value: {
                sampler: params.sampler || 'DPM++ 2M Karras',
                steps: params.steps || 32,
                cfg: params.cfg || 7.0,
                width: params.width,
                height: params.height,
                seed: params.seed === -1 ? Math.floor(Math.random() * 1000000) : params.seed
              }
            });
            break;
          case 'Output':
            newNodes.push({ ...node });
            break;
        }
      }
      const workflow = { ...workflowTemplate, nodes: newNodes };

      // Step 3: Send workflow to backend
      toast('📤 Sending to backend...')
      setProgress(30)

      const response = await fetch('/api/generate/text-to-image', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ workflow })
      })

      if (!response.ok) throw new Error(`Generation failed: ${response.statusText}`)
      const result = await response.json()

      if (result.success) {
        // If we have an image URL immediately, show it
        if (result.image_url) {
          const generationResult: GenerationResult = {
            id: Date.now().toString(),
            imageUrl: result.image_url,
            prompt: params.prompt,
            timestamp: new Date(),
            settings: {
              model: params.model,
              steps: params.steps,
              guidance: params.cfg,
              dimensions: `${params.width}x${params.height}`,
              seed: params.seed === -1 ? Math.floor(Math.random() * 1000000) : params.seed
            },
            status: 'completed'
          }
          setCurrentGeneration(generationResult)
          setProgress(100)
          setGenerationStatus('completed')
          setIsGenerating(false)
          setTimeout(() => setShowPostMenu(true), 1000)
        } else {
          // Generation started, wait for WebSocket updates
          toast.success('🚀 Generation started! Watch the terminal for progress.')
          setProgress(40)
          // The WebSocket will handle progress updates and completion
        }
      } else {
        throw new Error(result.error || 'Generation failed')
      }
    } catch (error) {
      console.error('Generation failed:', error)
      toast.error(`Generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
      setGenerationStatus('failed')
    } finally {
      setIsGenerating(false)
    }
  }

  const getModelType = (modelName: string): string => {
    const model = modelName.toLowerCase()

    // Flux models
    if (model.includes('flux')) return 'flux'

    // SDXL models
    if (model.includes('sdxl') ||
        model.includes('xl') ||
        model.includes('juggernaut') ||
        model.includes('realvis') ||
        model.includes('dreamshaper') ||
        model.includes('proteus')) return 'sdxl'

    // SD 1.5 models
    if (model.includes('sd15') ||
        model.includes('v1-5') ||
        model.includes('stable-diffusion-v1') ||
        model.includes('deliberate') ||
        model.includes('realistic') ||
        model.includes('anything')) return 'sd15'

    // Default to flux for unknown models (most modern)
    return 'flux'
  }

  const handleEnhancePrompt = async () => {
    if (!params.prompt.trim()) {
      toast.error('Please enter a prompt to enhance')
      return
    }

    setIsEnhancing(true)

    try {
      const modelType = getModelType(params.model)

      toast.loading('🤖 Enhancing prompt with AI...', { id: 'enhance-prompt' })

      const response = await fetch('/api/ollama/enhance-prompt', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          prompt: params.prompt,
          model: params.model,
          style_preferences: [modelType]
        })
      })

      if (response.ok) {
        const result = await response.json()
        setParams(prev => ({ ...prev, prompt: result.enhanced_prompt }))

        toast.success(
          `✨ Prompt enhanced for ${modelType.toUpperCase()} model!`,
          { id: 'enhance-prompt' }
        )

        // Show enhancement details in terminal
        console.log('Enhancement details:', {
          original: params.prompt,
          enhanced: result.enhanced_prompt,
          model: result.model_used,
          enhancementModel: result.enhancement_model
        })
      } else {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to enhance prompt')
      }
    } catch (error) {
      console.error('Prompt enhancement failed:', error)
      toast.error(
        `Failed to enhance prompt: ${error instanceof Error ? error.message : 'Unknown error'}`,
        { id: 'enhance-prompt' }
      )
    } finally {
      setIsEnhancing(false)
    }
  }

  return (
    <div className="flex h-full">
      {/* Left Panel - Controls */}
      <div className="w-96 border-r border-border bg-card p-6 overflow-y-auto">
        <div className="space-y-6">
          <div>
            <h2 className="text-xl font-semibold mb-2">Text to Image</h2>
            <p className="text-sm text-muted-foreground">
              Generate images from text descriptions using Flux models
            </p>
          </div>

          {/* Prompt */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <label className="text-sm font-medium">Prompt</label>
              <div className="flex items-center gap-2">
                <span className="text-xs text-slate-500">
                  {getModelType(params.model).toUpperCase()} optimized
                </span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleEnhancePrompt}
                  disabled={!params.prompt.trim() || isEnhancing}
                  className="relative"
                >
                  {isEnhancing ? (
                    <>
                      <RefreshCw className="w-4 h-4 mr-1 animate-spin" />
                      Enhancing...
                    </>
                  ) : (
                    <>
                      <Sparkles className="w-4 h-4 mr-1" />
                      Enhance Prompt
                    </>
                  )}
                </Button>
              </div>
            </div>
            <Textarea
              placeholder="Describe the image you want to generate..."
              value={params.prompt}
              onChange={(e) => setParams(prev => ({ ...prev, prompt: e.target.value }))}
              rows={4}
              className="resize-none"
            />
            <div className="text-xs text-slate-500">
              💡 Use the "Enhance Prompt" button to optimize your prompt for the selected {getModelType(params.model).toUpperCase()} model using llama3.2:latest
            </div>
          </div>

          {/* Negative Prompt */}
          <div className="space-y-2">
            <label className="text-sm font-medium">Negative Prompt</label>
            <Textarea
              placeholder="What to avoid in the image..."
              value={params.negativePrompt}
              onChange={(e) => setParams(prev => ({ ...prev, negativePrompt: e.target.value }))}
              rows={2}
              className="resize-none"
            />
          </div>

          {/* Model Selection */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <label className="text-sm font-medium">Model</label>
              <div className="flex items-center gap-2">
                <span className={`px-2 py-1 text-xs rounded-full font-medium ${
                  getModelType(params.model) === 'flux' ? 'bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-300' :
                  getModelType(params.model) === 'sdxl' ? 'bg-green-100 text-green-700 dark:bg-green-900 dark:text-green-300' :
                  'bg-purple-100 text-purple-700 dark:bg-purple-900 dark:text-purple-300'
                }`}>
                  {getModelType(params.model).toUpperCase()}
                </span>
              </div>
            </div>
            <div className="flex gap-2 items-center">
              <Select
                value={params.model}
                onChange={(e) => {
                  const model = e.target.value;
                  let vae = params.vae;
                  // List of all Flux model filenames (case-insensitive)
                  const fluxModelNames = [
                    'flux1-schnell.safetensors',
                    'fluxArtFusionNF4Fp8Fp16_v10Fp16.safetensors',
                    'fluxFillFP8_v10.safetensors',
                    'fluxmania_III(fluxDfp8).safetensors',
                    'flux1-dev.safetensors',
                    'flux1-kontext-dev-Q4_K_M.gguf',
                    'flux1-kontext-dev.safetensors',
                    'pixelwave_flux1_dev_fp8_03.safetensors'
                  ];
                  const modelFile = model.split(/\\|\//).pop()?.toLowerCase() || '';
                  if (fluxModelNames.map(n => n.toLowerCase()).includes(modelFile)) {
                    vae = 'L:\\ComfyUI\\models\\vae\\Flux_vae.safetensors';
                  } else {
                    vae = 'L:\\ComfyUI\\models\\vae\\sdxl_vae.safetensors';
                  }
                  setParams(prev => ({ ...prev, model, vae }));
                }}
                disabled={modelsLoading}
              >
                {modelsLoading ? (
                  <option>Loading...</option>
                ) : modelsError ? (
                  <option disabled>{modelsError}</option>
                ) : availableModels.length > 0 ? (
                  availableModels.map(model => (
                    <option key={model} value={model}>{model}</option>
                  ))
                ) : (
                  <option disabled>No models found</option>
                )}
              </Select>
              <Button size="icon" variant="ghost" onClick={fetchAvailableModels} disabled={modelsLoading} title="Reload models">
                <RefreshCw className={modelsLoading ? 'animate-spin' : ''} />
              </Button>
            </div>
            <div className="text-xs text-slate-500">
              {getModelType(params.model) === 'flux' && '🎨 FLUX: Best for natural language prompts, complex scenes, and photorealism'}
              {getModelType(params.model) === 'sdxl' && '🖼️ SDXL: Optimized for structured prompts with technical terms and quality boosters'}
              {getModelType(params.model) === 'sd15' && '🏷️ SD1.5: Works best with comma-separated tags and artist references'}
            </div>
          </div>
        <div className="flex flex-col gap-2">
          <label htmlFor="lora" className="font-semibold">LoRA</label>
          <div className="flex gap-2 items-center">
            <select
              id="lora"
              className="input"
              value={params.lora}
              onChange={e => setParams(p => ({ ...p, lora: e.target.value }))}
              disabled={modelsLoading}
            >
              <option value="">(None)</option>
              {modelsLoading ? (
                <option>Loading...</option>
              ) : modelsError ? (
                <option disabled>{modelsError}</option>
              ) : availableLoras.length > 0 ? (
                availableLoras.map(lora => (
                  <option key={lora} value={lora}>{lora}</option>
                ))
              ) : null}
            </select>
            <Button size="icon" variant="ghost" onClick={fetchAvailableModels} disabled={modelsLoading} title="Reload LoRA list">
              <RefreshCw className={modelsLoading ? 'animate-spin' : ''} />
            </Button>
          </div>
        </div>
        <div className="flex flex-col gap-2">
          <label htmlFor="vae" className="font-semibold">VAE</label>
          <select
            id="vae"
            className="input"
            value={params.vae}
            onChange={e => setParams(p => ({ ...p, vae: e.target.value }))}
          >
            <option value="">(Default)</option>
            {availableVAEs.map(vae => (
              <option key={vae} value={vae}>{vae}</option>
            ))}
          </select>
          </div>

          {/* Dimensions */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Width</label>
              <Select 
                value={params.width.toString()} 
                onChange={(e) => setParams(prev => ({ ...prev, width: parseInt(e.target.value) }))}
              >
                <option value="512">512</option>
                <option value="768">768</option>
                <option value="1024">1024</option>
                <option value="1536">1536</option>
              </Select>
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">Height</label>
              <Select 
                value={params.height.toString()} 
                onChange={(e) => setParams(prev => ({ ...prev, height: parseInt(e.target.value) }))}
              >
                <option value="512">512</option>
                <option value="768">768</option>
                <option value="1024">1024</option>
                <option value="1536">1536</option>
              </Select>
            </div>
          </div>


          {/* Advanced Settings */}
          <div className="space-y-4">
            <h3 className="text-sm font-medium flex items-center">
              <Settings className="w-4 h-4 mr-2" />
              Advanced Settings
            </h3>

            {/* Text Encoder T5 */}
            <div className="space-y-2">
              <label className="text-sm font-medium">Text Encoder T5</label>
              <input
                type="text"
                className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                value={params.textEncoderT5}
                onChange={e => setParams(prev => ({ ...prev, textEncoderT5: e.target.value }))}
                placeholder="Path to T5 encoder (.safetensors)"
              />
            </div>

            {/* Text Encoder ViT-L */}
            <div className="space-y-2">
              <label className="text-sm font-medium">Text Encoder ViT-L</label>
              <input
                type="text"
                className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                value={params.textEncoderViTL}
                onChange={e => setParams(prev => ({ ...prev, textEncoderViTL: e.target.value }))}
                placeholder="Path to ViT-L encoder (.safetensors)"
              />
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Steps</label>
              <Slider
                value={params.steps}
                onValueChange={(value) => setParams(prev => ({ ...prev, steps: value }))}
                min={1}
                max={50}
                step={1}
              />
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">CFG Scale</label>
              <Slider
                value={params.cfg}
                onValueChange={(value) => setParams(prev => ({ ...prev, cfg: value }))}
                min={1}
                max={20}
                step={0.5}
              />
            </div>

            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <label className="text-sm font-medium">Seed</label>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={generateRandomSeed}
                >
                  <RefreshCw className="w-4 h-4" />
                </Button>
              </div>
              <input
                type="number"
                className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                value={params.seed}
                onChange={(e) => setParams(prev => ({ ...prev, seed: parseInt(e.target.value) || -1 }))}
                placeholder="Random seed"
              />
            </div>
          </div>

          {/* Generate Button */}
          <Button
            onClick={handleGenerate}
            disabled={isGenerating || !params.prompt.trim()}
            className="w-full"
            size="lg"
          >
            {isGenerating ? (
              <>
                <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                Generating...
              </>
            ) : (
              <>
                <Wand2 className="w-4 h-4 mr-2" />
                Generate Image
              </>
            )}
          </Button>
        </div>
      </div>

      {/* Right Panel - Generated Image with Post-Generation Options */}
      <div className="flex-1 p-6 relative">
        <div className="h-full flex flex-col">
          {/* Header with Post Options Toggle */}
          <div className="flex items-center justify-between mb-6">
            <div>
              <h2 className="text-xl font-semibold">Generated Image</h2>
              <p className="text-sm text-slate-500 dark:text-slate-400">
                Your AI-generated artwork appears here
              </p>
            </div>
            
            {currentGeneration && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowPostMenu(true)}
                className="flex items-center gap-2"
              >
                <Menu className="w-4 h-4" />
                Options
              </Button>
            )}
          </div>

          {/* Generated Image Display */}
          <div className="flex-1">
            <GeneratedImageDisplay
              generation={currentGeneration}
              isGenerating={isGenerating}
              progress={generationProgress}
              onRegenerate={handleGenerate}
              onVariation={() => {
                toast('Variation feature coming soon!')
              }}
              onUpscale={() => {
                setShowPostMenu(true)
              }}
              onShowPostOptions={() => setShowPostMenu(true)}
            />
          </div>
        </div>

        {/* Post-Generation Menu */}
        <PostGenerationMenu
          generation={currentGeneration}
          isOpen={showPostMenu}
          onClose={() => setShowPostMenu(false)}
          onUpscale={() => {
            toast('Upscale feature coming soon!')
            setShowPostMenu(false)
          }}
          onInpaint={() => {
            toast('Inpaint feature coming soon!')
            setShowPostMenu(false)
          }}
          onOutpaint={() => {
            toast('Outpaint feature coming soon!')
            setShowPostMenu(false)
          }}
          onVariation={() => {
            toast('Variation feature coming soon!')
            setShowPostMenu(false)
          }}
          onImg2Img={() => {
            toast('Image-to-Image feature coming soon!')
            setShowPostMenu(false)
          }}
          onSaveToFavorites={() => {
            toast.success('Saved to favorites!')
            setShowPostMenu(false)
          }}
          onAddToCollection={() => {
            toast('Collection feature coming soon!')
            setShowPostMenu(false)
          }}
        />
      </div>
    </div>
  )
}
