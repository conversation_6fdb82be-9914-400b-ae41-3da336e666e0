import { NextResponse } from 'next/server'

// This is a simple in-memory log buffer for demonstration. In production, use a persistent log source or WebSocket.
let logBuffer: string[] = []

// Simulate log streaming: append logs from the backend (in production, poll or subscribe to backend logs)
export async function GET() {
  // In a real app, fetch logs from backend or a log file
  // For now, just return the buffer
  return NextResponse.json({ logs: logBuffer })
}

// Endpoint to add a log (for backend to POST to)
export async function POST(req: Request) {
  const { message, type } = await req.json()
  if (typeof message === 'string') {
    const timestamp = new Date().toLocaleTimeString()
    const formattedMessage = `[${timestamp}] ${message}`
    logBuffer.push(formattedMessage)
    // Keep buffer size reasonable
    if (logBuffer.length > 200) logBuffer = logBuffer.slice(-200)
  }
  return NextResponse.json({ ok: true })
}
