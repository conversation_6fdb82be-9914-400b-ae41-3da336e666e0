LLM Coding Instructions for the ComfyUI Custom API-Driven Frontend

Provide project context and coding guidelines that AI should follow when generating code, answering questions, or reviewing changes.

## Overview
Develop a modern, robust, and professional web frontend for ComfyUI, focusing on seamless API integration, broad support for core and advanced image generation features, and an extensible, customizable user experience. The primary objectives are to deliver a user-friendly interface, full access to ComfyUI's capabilities (image generation modes and extensions), curated model control, and creative prompt enhancement—eschewing the traditional node-editor model in favor of standard, intuitive UI paradigms.

## 1. Project Architecture
Frontend Stack: Next.js 14, TypeScript, Tailwind CSS (or comparable modern alternatives).

Backend: FastAPI for service layer (Python 3.11, virtual environment enforced), acts as middleware/API orchestrator to ComfyUI and LLMs.

State Management: Use React Context or similar robust solutions for workspace and user settings persistence.

Component Organization: Group by feature/module; separate container and presentational components for maintainable scaling.

CORS, Routing, and Error Handling: Ensure cross-service compatibility, API proxying, and implement clear error boundaries for all remote calls.

## 2. Core Feature Guidelines

### A. Image Generation Modes
Support the following ComfyUI workflows as distinct, user-facing modes:

| Mode | Inputs | Outputs | Typical Use Cases |
|------|--------|---------|-------------------|
| Text-to-Image | Text prompt | Image | Artwork, illustration, concept art |
| Image-to-Image | Image, text prompt | Modified image | Style transfer, refinement |
| Inpainting | Image, mask, text | Edited image | Object removal, local repair |
| Outpainting | Image, mask, text | Expanded image | Panoramas, contextual expansion |
| **Flux Kontext Dev** | **Image, context prompt** | **Edited image** | **Multimodal image editing, object modification, style consistency** |

**Flux Kontext Integration**: Support for Flux.1 Kontext Dev model for advanced multimodal image editing with multiple format support (original 24GB, FP8 12GB, GGUF quantized versions). Enable continuous image editing, precise object modification, character consistency editing, and composition control.

Present mode selection as a clear, persistent workspace control.

Dynamically adjust the input section based on workflow requirements (image upload, masking tools, prompt fields).

All models, workflows, and extensions should be selectable based on available local resources.

### B. Extension & Module Integration
Enable core enhancements via toggles/sections in the UI:

ControlNet: Allow stacking, model type selection, uploading/processing conditioning images, adjusting control strength.

IPAdapter: Provide for reference image upload, style/similarity weight adjustment.

LoRA, Custom Nodes: Discover and display all extensions on disk, with configuration UI.

Image Enhancement/Upscaling: Integrate upscaling as both a separate workflow and a post-process option.

### C. Model & Resource Management
Scan all known model directories; list models (SDXL, custom, GGUF, etc.), LoRAs, ControlNets, and IPAdapters.

**Alternative Frontend Approaches**: Be aware of existing alternative ComfyUI frontends like comfyui-facade-editor (Next.js-based simplified interface), EasyComfyUI (workflow-based apps), and various API wrapper projects. Study these approaches for UI/UX inspiration while maintaining API-first architecture.

**Frontend Package Management**: Leverage the official comfyui-frontend-package for consistent versioning and compatibility with ComfyUI updates. The package supports independent frontend updates without backend disruption.

Expose a curated list of installed LLMs (Ollama, Mistral, etc.) with on-demand scan and management features.

Support flexible referencing of models (absolute and relative paths), with clear presentation of VRAM requirements and compatible inputs.

All large files reside on dedicated drives—ensure correct path translation and access permissions.

## 3. Creative Prompt Generation
Implement AI-powered prompt enhancement, leveraging the user's installed local LLMs.

Offer both manual prompt editing and AI-assisted creative/semantic prompt generation mode with advanced features (randomization, style/mood/lighting options).

Allow users to save/load prompt-generation strategies and premade randomization techniques.

Enable fetching and displaying reference image suggestions for enhanced inspiration.

Provide a dedicated reference image mode where users can upload/select an image and adjust the influence (similarity, color palette, style strength, etc.).

## 4. API & Real-Time Integration

**Workflow Definition**: Always require workflows exported as API-compatible JSON. Clearly state that direct node manipulation does not occur in the frontend; all UI actions construct or modify these workflow objects for API submission.

All backend calls interact with ComfyUI via API-exported workflows; ensure endpoints, payloads, and parameterizations match the latest API schema.

**Job Submission and Progress**: Emphasize that live job status/results must be tracked using WebSockets (/ws), as REST polling alone may miss intermediate updates or extended runtimes.

Enable real-time progress feedback (via WebSockets or polling) during generation/inference.

**Model/Asset Paths**: Workflow payloads must provide absolute, backend-accessible file paths to all models and assets; arbitrary or relative paths may cause silent failures.

**API Node Use**: If integrating third-party APIs, these must be configured and installed as API Nodes and referenced in the API workflow as with any regular task node.

**ComfyUI Frontend Evolution**: Note that ComfyUI has transitioned to a new UI as the default (November 2024) with the official frontend now maintained at [Comfy-Org/ComfyUI_frontend](https://github.com/Comfy-Org/ComfyUI_frontend). The new frontend uses Vue 3, TypeScript, Pinia state management, PrimeVue with TailwindCSS, and litegraph.js for the node editor.

Handle empty, invalid, or error responses gracefully with user-friendly messages and actionable debugging info when in development mode.

## 5. User Experience
Maintain a professional, accessible, and highly customizable interface.

Ensure WCAG-compliant accessibility: label all controls, enable full keyboard navigation, and implement scalable color schemes.

Provide integrated help: embed tooltips, quick-start guides, and direct links to documentation where relevant.

Allow extensive user configurability per workflow, including default settings, preset templates, and history management.

## 6. Performance & Optimization
Leverage hardware capabilities: optimize for high VRAM, multi-core CPUs, and storage performance.

Use lazy loading and virtualization for heavy modules and large image outputs.

Employ robust debounce/throttle strategies for input-driven API calls.

Profile resource use and memory consumption, especially with multi-model and high-resolution workflows.

## 7. Development Practices
Enforce clear, typed contracts for all API interactions and state objects.

**Error Handling**: UI must be prepared for a wide range of REST/WebSocket errors, including silent {} objects in case of failure (especially for invalid workflow definitions or inaccessible files), and must expose descriptive/logged errors to users where possible.

Integrate comprehensive error logging (frontend and backend), covering both handled and unhandled failures.

**Version Sync**: Always track recent updates as ComfyUI, its official frontend, and the API schema are subject to frequent, sometimes breaking changes.

**API Integration Best Practices**: Follow established patterns from successful ComfyUI API implementations - use WebSocket connections for real-time updates, implement proper workflow queue management, and handle multi-step generation processes gracefully.

Use environment-specific configuration files for all critical paths and service endpoints.

Document all components, API endpoints, and custom model/extension logic as markdown within the /docs directory.

Write clean, modular code with separation of UI, logic, and side-effect handling.

Include test cases for key UI flows, API integration points, and all prompt enhancement logic.

## 8. References & Documentation Embedding
Embed or link official ComfyUI, ControlNet, IPAdapter, and model-specific documentation prominently in-app for user and developer reference.

Clearly surface licensing terms and community guidelines as required.

Keep all help, docs, and README files up to date with app evolution and track ComfyUI API changes regularly.

**Version Compatibility**: Maintain awareness of ComfyUI version compatibility and API schema changes, as the ecosystem evolves frequently with potential breaking changes.

Following these high-level instructions ensures the custom ComfyUI frontend is API-first, highly usable, fully featured, and extensible, with best practices for both UI/UX and LLM-powered creative enhancements.