from fastapi import <PERSON><PERSON><PERSON>, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
import uvicorn
import asyncio
import json
from typing import Dict, List
from contextlib import asynccontextmanager
import logging
from app.utils.frontend_log import post_log_to_frontend, set_websocket_manager

from app.api.routes import router as api_router
from app.core.config import settings
from app.services.comfyui_service import ComfyUIService
from app.services.ollama_service import OllamaService
from app.services.system_monitor import SystemMonitor

# Configure logging
logging.basicConfig(level=logging.INFO)
logging.getLogger("httpx").setLevel(logging.WARNING)
logger = logging.getLogger(__name__)

# Initialize services
comfyui_service = ComfyUIService()
ollama_service = OllamaService()
system_monitor = SystemMonitor()

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Handles application startup and shutdown events."""
    logger.info("Starting ComfyUI Custom Frontend API...")
    post_log_to_frontend("Starting ComfyUI Custom Frontend API...")
    # Start system monitoring
    asyncio.create_task(system_monitor.start_monitoring())
    # Check ComfyUI connection
    if await comfyui_service.check_connection():
        logger.info("ComfyUI connection established")
        post_log_to_frontend("ComfyUI connection established")
    else:
        logger.warning("ComfyUI connection failed - check if ComfyUI is running")
        post_log_to_frontend("ComfyUI connection failed - check if ComfyUI is running")
    # Check Ollama connection
    if await ollama_service.check_connection():
        logger.info("Ollama connection established")
        post_log_to_frontend("Ollama connection established")
        models = await ollama_service.list_models()
        logger.info(f"Available Ollama models: {[m['name'] for m in models]}")
        post_log_to_frontend(f"Available Ollama models: {[m['name'] for m in models]}")
    else:
        logger.warning("Ollama connection failed - check if Ollama is running")
        post_log_to_frontend("Ollama connection failed - check if Ollama is running")
    yield
    logger.info("Shutting down ComfyUI Custom Frontend API...")
    post_log_to_frontend("Shutting down ComfyUI Custom Frontend API...")
    await system_monitor.stop_monitoring()

app = FastAPI(
    title="ComfyUI Custom Frontend API",
    description="Backend API for ComfyUI Custom Frontend with AI-powered features",
    version="1.0.0",
    lifespan=lifespan
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allow all origins for development
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include API routes
app.include_router(api_router, prefix="/api/v1")

# Root level health endpoint (for compatibility)
@app.get("/health")
async def root_health():
    """Root level health check"""
    from app.services.comfyui_service import ComfyUIService
    from app.services.ollama_service import OllamaService
    import time
    
    comfyui_service = ComfyUIService()
    ollama_service = OllamaService()
    
    comfyui_status = await comfyui_service.check_connection()
    ollama_status = await ollama_service.check_connection()
    
    return {
        "status": "healthy",
        "services": {
            "comfyui": "connected" if comfyui_status else "disconnected",
            "ollama": "connected" if ollama_status else "disconnected"
        },
        "timestamp": time.time()
    }

# WebSocket connections manager
class ConnectionManager:
    def __init__(self):
        self.active_connections: List[WebSocket] = []

    async def connect(self, websocket: WebSocket):
        await websocket.accept()
        self.active_connections.append(websocket)
        logger.info(f"Client connected. Total connections: {len(self.active_connections)}")
        post_log_to_frontend(f"Client connected. Total connections: {len(self.active_connections)}")

    def disconnect(self, websocket: WebSocket):
        self.active_connections.remove(websocket)
        logger.info(f"Client disconnected. Total connections: {len(self.active_connections)}")
        post_log_to_frontend(f"Client disconnected. Total connections: {len(self.active_connections)}")

    async def send_personal_message(self, message: str, websocket: WebSocket):
        await websocket.send_text(message)

    async def broadcast(self, message: str):
        for connection in self.active_connections:
            try:
                await connection.send_text(message)
            except Exception as e:
                logger.error(f"Error broadcasting message: {e}")
                post_log_to_frontend(f"Error broadcasting message: {e}")

manager = ConnectionManager()

# Set the WebSocket manager for frontend logging
set_websocket_manager(manager)

@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    await manager.connect(websocket)
    try:
        while True:
            data = await websocket.receive_text()
            message_data = json.loads(data)
            
            # Handle different message types
            if message_data.get("type") == "ping":
                await manager.send_personal_message(
                    json.dumps({"type": "pong", "timestamp": message_data.get("timestamp")}),
                    websocket
                )
            elif message_data.get("type") == "subscribe_system_stats":
                # Send current system stats
                stats = await system_monitor.get_current_stats()
                await manager.send_personal_message(
                    json.dumps({"type": "system_stats", "data": stats}),
                    websocket
                )
                
    except WebSocketDisconnect:
        manager.disconnect(websocket)
    except Exception as e:
        logger.error(f"WebSocket error: {e}")
        post_log_to_frontend(f"WebSocket error: {e}")
        manager.disconnect(websocket)

@app.get("/")
async def root():
    return {
        "message": "ComfyUI Custom Frontend API",
        "version": "1.0.0",
        "status": "running"
    }

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    comfyui_status = await comfyui_service.check_connection()
    ollama_status = await ollama_service.check_connection()
    
    return {
        "status": "healthy",
        "services": {
            "comfyui": "connected" if comfyui_status else "disconnected",
            "ollama": "connected" if ollama_status else "disconnected"
        },
        "system": await system_monitor.get_current_stats()
    }

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
