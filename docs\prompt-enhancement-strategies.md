# Prompt Enhancement Strategies

This document outlines the model-specific prompt enhancement strategies implemented in the Ollama service.

## Overview

The prompt enhancement system uses `llama3.2:latest` to optimize prompts based on the target image generation model. Each model type has specific characteristics and responds better to different prompting approaches.

## Model-Specific Strategies

### FLUX Models
**Models**: flux1-dev, flux1-schnell, flux-kontext-dev, etc.

**Characteristics**:
- Excellent natural language understanding
- Superior at complex scene composition
- Great text rendering capabilities
- Advanced lighting and atmospheric effects
- Handles multiple subjects well

**Enhancement Strategy**:
1. Use natural, descriptive language (not comma-separated tags)
2. Include specific lighting conditions (golden hour, studio lighting)
3. Add composition details (close-up, wide shot, bird's eye view)
4. Specify camera settings (shallow depth of field, bokeh)
5. Include atmospheric elements (fog, rain, sunbeams)
6. Add material and texture descriptions
7. Specify art styles naturally ("in the style of", "reminiscent of")

**Example Enhancement**:
- **Input**: "a cat sitting on a chair"
- **Enhanced**: "A majestic tabby cat sitting gracefully on a vintage wooden chair, bathed in warm golden hour sunlight streaming through a nearby window, creating soft shadows and highlighting the cat's fur texture, shot with shallow depth of field creating a dreamy bokeh background"

### SDXL Models
**Models**: sdxl, juggernaut-xl, realvisxl, etc.

**Characteristics**:
- Works best with structured, detailed prompts
- Responds well to technical photography terms
- Benefits from quality boosters
- Good at artistic style references

**Enhancement Strategy**:
1. Start with clear subject definition
2. Add technical quality terms (masterpiece, best quality, highly detailed)
3. Include specific art styles or mediums
4. Add camera and lens specifications
5. Specify lighting setup
6. Include composition terms
7. Add texture and material details

**Example Enhancement**:
- **Input**: "a cat sitting on a chair"
- **Enhanced**: "masterpiece, best quality, highly detailed, a beautiful tabby cat sitting on an antique wooden chair, professional photography, 85mm lens, f/1.4 aperture, studio lighting with soft rim lighting, rule of thirds composition, rich textures, polished wood grain, soft fur details"

### SD 1.5 Models
**Models**: stable-diffusion-v1-5, sd15, etc.

**Characteristics**:
- Responds well to comma-separated tag structure
- Benefits from specific artist references
- Needs quality and aesthetic boosters
- Works with weighted emphasis using parentheses

**Enhancement Strategy**:
1. Structure as comma-separated tags
2. Start with subject and main action
3. Add quality boosters
4. Include specific artist references
5. Add technical terms (8k, uhd, professional photography)
6. Specify medium (digital art, oil painting)
7. Include lighting and mood descriptors

**Example Enhancement**:
- **Input**: "a cat sitting on a chair"
- **Enhanced**: "tabby cat, sitting, wooden chair, masterpiece, best quality, ultra detailed, 8k, professional photography, by Annie Leibovitz, digital art, warm lighting, cozy atmosphere, detailed fur texture, vintage furniture, soft shadows"

## Implementation Details

### Backend Service
The enhancement logic is implemented in `backend/app/services/ollama_service.py`:

```python
def _get_model_specific_enhancement_prompt(self, model_name: str) -> str:
    # Returns appropriate system prompt based on model type
    
async def enhance_prompt(self, base_prompt: str, model_name: str = "flux", style_preferences: List[str] = None) -> str:
    # Uses llama3.2:latest to enhance prompts with model-specific strategies
```

### Frontend Integration
The frontend automatically detects the selected model and applies appropriate enhancement:

```typescript
const getModelType = (modelName: string): string => {
    // Detects model type from filename
}

const handleEnhancePrompt = async () => {
    // Sends model information to backend for targeted enhancement
}
```

## Usage

1. **Manual Enhancement**: Click the "Enhance Prompt" button to optimize your prompt for the selected model
2. **Automatic Enhancement**: During generation, prompts are automatically enhanced based on the selected model
3. **Model-Aware**: The system shows which model type optimization is being used

## Benefits

- **Model-Optimized**: Each enhancement is tailored to the specific model's strengths
- **Improved Quality**: Better prompts lead to higher quality generated images
- **User-Friendly**: Automatic detection and enhancement with clear feedback
- **Flexible**: Works with manual enhancement or automatic during generation

## Future Enhancements

- Support for additional model types (Midjourney-style, etc.)
- User-customizable enhancement templates
- A/B testing of different enhancement strategies
- Integration with LoRA-specific enhancements
